<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Audio Review Phase</title>
    <link rel="stylesheet" href="/static/css/base.css">
    <link rel="stylesheet" href="/static/css/dashboard.css">
    <link rel="stylesheet" href="/static/css/memes.css">
</head>
<body>
    {% include 'navbar.html' %}
    <div class="container">
        <!-- Flash Messages -->
        {% if session.flash_message and ('workflow_audio' in session.flash_context or 'audio_workshop' in session.flash_context) %}
        <div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 6px; margin: 20px 0;">
            {{ session.flash_message }}
            {% set _ = session.pop('flash_message') %}
            {% set _ = session.pop('flash_context') %}
        </div>
        {% endif %}

        <h1>🎵 Audio Review Phase</h1>
        <p>Generate audio for memes with text, then review and approve them for video generation.</p>

        {% if memes_by_subreddit %}
            <!-- Bulk Audio Generation -->
            <div class="bulk-actions">
                <button type="button" class="btn btn-primary" onclick="generateAllAudio()">🎵 Generate Audio for All Memes</button>
            </div>

            {% for subreddit, memes in memes_by_subreddit.items() %}
            <div class="subreddit-section">
                <h2>📂 r/{{ subreddit }} ({{ memes|length }} memes with text)</h2>

                {% for meme in memes %}
                <div class="meme-editor">
                    <div class="meme-image">
                        <img src="{{ meme.url }}" alt="Meme Image"
                             onclick="openImageModal(this.src)"
                             style="max-width: 400px; max-height: 300px; object-fit: contain; cursor: pointer;">
                    </div>
                    <div class="meme-text-editor">
                        <div class="meme-status">
                            {% if meme.audio_approved %}
                                <span class="status-approved">✅ Audio approved for video generation</span>
                            {% else %}
                                <span class="status-pending">⏳ Pending audio approval</span>
                            {% endif %}
                        </div>

                        <div class="meme-text-content">
                            <h4>Text for narration:</h4>
                            <p class="meme-text">{{ meme.text }}</p>
                        </div>

                        <div class="audio-preview">
                            {% set audio_path = meme.audio_path or session.get('audio_path_' + meme.id|string) %}
                            {% if audio_path %}
                                <h4>Generated Audio:</h4>
                                <audio controls style="width: 100%; margin-bottom: 1rem;">
                                    <source src="/{{ audio_path }}" type="audio/wav">
                                    Your browser does not support the audio tag.
                                </audio>
                            {% else %}
                                <p><em>No audio generated yet. Click "Generate Audio" to create audio for this meme.</em></p>
                            {% endif %}
                        </div>

                        <div class="workflow-actions">
                            {% if not audio_path %}
                            <button type="button" class="btn btn-secondary" onclick="generateAudio({{ meme.id }})">🎵 Generate Audio</button>
                            {% endif %}

                            {% if audio_path and not meme.audio_approved %}
                            <button type="button" class="btn btn-success" onclick="approveAudio({{ meme.id }})">✅ Approve Audio & Continue</button>
                            {% endif %}

                            <button type="button" class="btn btn-danger" onclick="discardMeme({{ meme.id }})">🗑️ Discard</button>
                        </div>
                    </div>
                </div>
                <hr>
                {% endfor %}
            </div>
            {% endfor %}

            <div class="actions">
                <a href="/meme_workshop" class="btn btn-secondary">⬅️ Back to Meme Workshop</a>
                <a href="/dashboard" class="btn btn-primary">🏠 Back to Dashboard</a>
            </div>
        {% else %}
            <p>No memes with text are ready for audio review.
               <a href="/meme_workshop" class="btn btn-primary">🎭 Go to Meme Workshop</a> to fetch and approve memes first!</p>
        {% endif %}
    </div>

    <!-- Image Modal -->
    <div id="imageModal" class="image-modal">
        <span class="image-modal-close" onclick="closeImageModal()">&times;</span>
        <img class="image-modal-content" id="modalImage">
    </div>

    <script>
        function openImageModal(src) {
            const modal = document.getElementById('imageModal');
            const modalImg = document.getElementById('modalImage');
            modal.classList.add('show');
            modalImg.src = src;
        }

        function closeImageModal() {
            const modal = document.getElementById('imageModal');
            modal.classList.remove('show');
        }

        // Close modal when clicking outside the image
        document.getElementById('imageModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeImageModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeImageModal();
            }
        });

        // Helper function to update subreddit meme count
        function updateSubredditCount(subredditSection) {
            const memeCount = subredditSection.querySelectorAll('.meme-editor').length;
            const header = subredditSection.querySelector('h2');
            if (header) {
                const subredditName = header.textContent.match(/r\/(\w+)/)[1];
                header.textContent = `📂 r/${subredditName} (${memeCount} memes)`;
            }
        }

        // Helper function to remove meme from page with animation
        function removeMemeFromPage(memeEditor) {
            memeEditor.style.transition = 'opacity 0.3s ease';
            memeEditor.style.opacity = '0';
            setTimeout(() => {
                const subredditSection = memeEditor.closest('.subreddit-section');
                memeEditor.remove();

                // Update meme count in subreddit header
                if (subredditSection) {
                    updateSubredditCount(subredditSection);

                    // Check if subreddit section is empty and remove it
                    if (subredditSection.querySelectorAll('.meme-editor').length === 0) {
                        subredditSection.remove();
                    }
                }
            }, 300);
        }

        // Generate audio for a specific meme
        function generateAudio(memeId) {
            const button = document.querySelector(`button[onclick="generateAudio(${memeId})"]`);
            const originalText = button.innerHTML;

            // Show loading state
            button.innerHTML = '⏳ Generating...';
            button.disabled = true;

            fetch('/generate_audio', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `meme_id=${memeId}`
            }).then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Reload the page to show the generated audio
                    location.reload();
                } else {
                    alert('Error generating audio: ' + (data.message || 'Unknown error'));
                    button.innerHTML = originalText;
                    button.disabled = false;
                }
            }).catch(error => {
                console.error('Error:', error);
                alert('Error generating audio. Please try again.');
                button.innerHTML = originalText;
                button.disabled = false;
            });
        }

        // Generate audio for all memes
        function generateAllAudio() {
            const button = document.querySelector(`button[onclick="generateAllAudio()"]`);
            const originalText = button.innerHTML;

            // Show loading state
            button.innerHTML = '⏳ Generating All...';
            button.disabled = true;

            fetch('/generate_audio', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'generate_all=true'
            }).then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Reload the page to show all generated audio
                    location.reload();
                } else {
                    alert('Error generating audio: ' + (data.message || 'Unknown error'));
                    button.innerHTML = originalText;
                    button.disabled = false;
                }
            }).catch(error => {
                console.error('Error:', error);
                alert('Error generating audio. Please try again.');
                button.innerHTML = originalText;
                button.disabled = false;
            });
        }

        // Approve audio and remove from page
        function approveAudio(memeId) {
            fetch('/approve_meme_audio', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `meme_id=${memeId}`
            }).then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Find the meme editor element and remove it
                    const memeEditor = document.querySelector(`button[onclick="approveAudio(${memeId})"]`).closest('.meme-editor');
                    removeMemeFromPage(memeEditor);
                } else {
                    alert('Error approving audio: ' + data.message);
                }
            }).catch(error => {
                console.error('Error:', error);
                alert('Error approving audio. Please try again.');
            });
        }

        // Discard meme and remove from page
        function discardMeme(memeId) {
            if (!confirm('Are you sure you want to discard this meme?')) {
                return;
            }

            fetch('/discard_meme', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `meme_id=${memeId}`
            }).then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Find the meme editor element and remove it
                    const memeEditor = document.querySelector(`button[onclick="discardMeme(${memeId})"]`).closest('.meme-editor');
                    removeMemeFromPage(memeEditor);
                } else {
                    alert('Error discarding meme: ' + data.message);
                }
            }).catch(error => {
                console.error('Error:', error);
                alert('Error discarding meme. Please try again.');
            });
        }
    </script>
</body>
</html>
